// Hook for development tools functionality
import { getEnvironmentInfo } from '@/lib/config';
import { performanceMonitor } from '@/lib/performance';
import { logger, LogLevel } from '@/lib/logger';

export function useDevTools() {
  const envInfo = getEnvironmentInfo();
  const performanceSummary = performanceMonitor.getPerformanceSummary();

  // Convert LogLevel enum to string for the UI
  const convertLogLevelToString = (level: LogLevel): string => {
    switch (level) {
      case LogLevel.DEBUG:
        return 'debug';
      case LogLevel.INFO:
        return 'info';
      case LogLevel.WARN:
        return 'warn';
      case LogLevel.ERROR:
        return 'error';
      default:
        return 'info';
    }
  };

  const recentLogs = logger.getLogs().slice(-5).map(log => ({
    ...log,
    level: convertLogLevelToString(log.level),
    timestamp: log.timestamp.toISOString(),
  }));

  const exportData = () => {
    const data = {
      environment: envInfo,
      performance: performanceSummary,
      logs: logger.getLogs().map(log => ({
        ...log,
        level: convertLogLevelToString(log.level),
        timestamp: log.timestamp.toISOString(),
      })),
      timestamp: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sologrind-dev-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return {
    envInfo,
    performanceSummary,
    recentLogs,
    exportData,
  };
}
